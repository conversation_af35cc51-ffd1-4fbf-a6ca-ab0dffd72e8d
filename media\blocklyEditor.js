// Blockly Editor WebView <PERSON>
(function() {
    const vscode = acquireVsCodeApi();
    let workspace;
    let currentLanguage = 'JavaScript';

    // Initialize Blockly workspace
    function initializeBlockly() {
        const toolbox = {
            "kind": "categoryToolbox",
            "contents": [
                {
                    "kind": "category",
                    "name": "Logic",
                    "colour": "#5C81A6",
                    "contents": [
                        {
                            "kind": "block",
                            "type": "controls_if"
                        },
                        {
                            "kind": "block",
                            "type": "logic_compare"
                        },
                        {
                            "kind": "block",
                            "type": "logic_operation"
                        },
                        {
                            "kind": "block",
                            "type": "logic_negate"
                        },
                        {
                            "kind": "block",
                            "type": "logic_boolean"
                        },
                        {
                            "kind": "block",
                            "type": "logic_null"
                        },
                        {
                            "kind": "block",
                            "type": "logic_ternary"
                        }
                    ]
                },
                {
                    "kind": "category",
                    "name": "Loops",
                    "colour": "#5CA65C",
                    "contents": [
                        {
                            "kind": "block",
                            "type": "controls_repeat_ext"
                        },
                        {
                            "kind": "block",
                            "type": "controls_whileUntil"
                        },
                        {
                            "kind": "block",
                            "type": "controls_for"
                        },
                        {
                            "kind": "block",
                            "type": "controls_forEach"
                        },
                        {
                            "kind": "block",
                            "type": "controls_flow_statements"
                        }
                    ]
                },
                {
                    "kind": "category",
                    "name": "Math",
                    "colour": "#5C68A6",
                    "contents": [
                        {
                            "kind": "block",
                            "type": "math_number"
                        },
                        {
                            "kind": "block",
                            "type": "math_arithmetic"
                        },
                        {
                            "kind": "block",
                            "type": "math_single"
                        },
                        {
                            "kind": "block",
                            "type": "math_trig"
                        },
                        {
                            "kind": "block",
                            "type": "math_constant"
                        },
                        {
                            "kind": "block",
                            "type": "math_number_property"
                        },
                        {
                            "kind": "block",
                            "type": "math_round"
                        },
                        {
                            "kind": "block",
                            "type": "math_on_list"
                        },
                        {
                            "kind": "block",
                            "type": "math_modulo"
                        },
                        {
                            "kind": "block",
                            "type": "math_constrain"
                        },
                        {
                            "kind": "block",
                            "type": "math_random_int"
                        },
                        {
                            "kind": "block",
                            "type": "math_random_float"
                        }
                    ]
                },
                {
                    "kind": "category",
                    "name": "Text",
                    "colour": "#5CA68D",
                    "contents": [
                        {
                            "kind": "block",
                            "type": "text"
                        },
                        {
                            "kind": "block",
                            "type": "text_join"
                        },
                        {
                            "kind": "block",
                            "type": "text_append"
                        },
                        {
                            "kind": "block",
                            "type": "text_length"
                        },
                        {
                            "kind": "block",
                            "type": "text_isEmpty"
                        },
                        {
                            "kind": "block",
                            "type": "text_indexOf"
                        },
                        {
                            "kind": "block",
                            "type": "text_charAt"
                        },
                        {
                            "kind": "block",
                            "type": "text_getSubstring"
                        },
                        {
                            "kind": "block",
                            "type": "text_changeCase"
                        },
                        {
                            "kind": "block",
                            "type": "text_trim"
                        },
                        {
                            "kind": "block",
                            "type": "text_print"
                        }
                    ]
                },
                {
                    "kind": "category",
                    "name": "Lists",
                    "colour": "#745CA6",
                    "contents": [
                        {
                            "kind": "block",
                            "type": "lists_create_with"
                        },
                        {
                            "kind": "block",
                            "type": "lists_create_empty"
                        },
                        {
                            "kind": "block",
                            "type": "lists_repeat"
                        },
                        {
                            "kind": "block",
                            "type": "lists_length"
                        },
                        {
                            "kind": "block",
                            "type": "lists_isEmpty"
                        },
                        {
                            "kind": "block",
                            "type": "lists_indexOf"
                        },
                        {
                            "kind": "block",
                            "type": "lists_getIndex"
                        },
                        {
                            "kind": "block",
                            "type": "lists_setIndex"
                        },
                        {
                            "kind": "block",
                            "type": "lists_getSublist"
                        },
                        {
                            "kind": "block",
                            "type": "lists_split"
                        },
                        {
                            "kind": "block",
                            "type": "lists_sort"
                        }
                    ]
                },
                {
                    "kind": "category",
                    "name": "Variables",
                    "colour": "#A65C81",
                    "custom": "VARIABLE"
                },
                {
                    "kind": "category",
                    "name": "Functions",
                    "colour": "#9A5CA6",
                    "custom": "PROCEDURE"
                }
            ]
        };

        workspace = Blockly.inject('blocklyDiv', {
            toolbox: toolbox,
            collapse: true,
            comments: true,
            disable: true,
            maxBlocks: Infinity,
            trashcan: true,
            horizontalLayout: false,
            toolboxPosition: 'start',
            css: true,
            media: 'https://unpkg.com/blockly/media/',
            rtl: false,
            scrollbars: true,
            sounds: true,
            oneBasedIndex: true,
            grid: {
                spacing: 20,
                length: 3,
                colour: '#ccc',
                snap: true
            },
            zoom: {
                controls: true,
                wheel: true,
                startScale: 1.0,
                maxScale: 3,
                minScale: 0.3,
                scaleSpeed: 1.2
            }
        });

        // Listen for changes in the workspace
        workspace.addChangeListener(updateCode);
        workspace.addChangeListener(saveWorkspace);
    }

    // Update generated code display
    function updateCode() {
        try {
            let code = '';
            switch (currentLanguage) {
                case 'JavaScript':
                    code = Blockly.JavaScript.workspaceToCode(workspace);
                    break;
                case 'Python':
                    code = Blockly.Python.workspaceToCode(workspace);
                    break;
                case 'Dart':
                    code = Blockly.Dart.workspaceToCode(workspace);
                    break;
                case 'Lua':
                    code = Blockly.Lua.workspaceToCode(workspace);
                    break;
                case 'PHP':
                    code = Blockly.PHP.workspaceToCode(workspace);
                    break;
                default:
                    code = 'Language not supported';
            }
            document.getElementById('codeOutput').textContent = code;
        } catch (error) {
            console.error('Error generating code:', error);
            document.getElementById('codeOutput').textContent = 'Error generating code: ' + error.message;
        }
    }

    // Save workspace to VS Code document
    function saveWorkspace() {
        try {
            const xml = Blockly.Xml.workspaceToDom(workspace);
            const xmlText = Blockly.Xml.domToText(xml);
            
            const content = JSON.stringify({
                blocks: {
                    languageVersion: 0,
                    xml: xmlText
                },
                generatedCode: document.getElementById('codeOutput').textContent,
                metadata: {
                    language: currentLanguage,
                    lastModified: new Date().toISOString()
                }
            }, null, 2);

            vscode.postMessage({
                type: 'save',
                content: content
            });
        } catch (error) {
            console.error('Error saving workspace:', error);
        }
    }

    // Load workspace from content
    function loadWorkspace(content) {
        try {
            const data = JSON.parse(content);
            if (data.blocks && data.blocks.xml) {
                const xml = Blockly.Xml.textToDom(data.blocks.xml);
                Blockly.Xml.domToWorkspace(xml, workspace);
            }
            if (data.metadata && data.metadata.language) {
                currentLanguage = data.metadata.language;
                document.getElementById('languageSelect').value = currentLanguage;
            }
            updateCode();
        } catch (error) {
            console.error('Error loading workspace:', error);
        }
    }

    // Import handling functions
    function handleFileImported(code, language, fileName) {
        console.log('File imported:', fileName, 'Language:', language);

        // Show a confirmation dialog
        const shouldImport = confirm(`Import code from ${fileName}?\n\nThis will add blocks to your current workspace.`);
        if (!shouldImport) {
            return;
        }

        // Send code to VS Code for parsing
        vscode.postMessage({
            type: 'import-code',
            code: code,
            language: language
        });
    }

    function handleBlocksParsed(blocksXml) {
        try {
            console.log('Blocks parsed:', blocksXml);

            // Parse the XML and add blocks to workspace
            const xml = Blockly.Xml.textToDom(blocksXml);
            const blocks = xml.getElementsByTagName('block');

            // Position imported blocks to avoid overlap
            let maxY = 20;
            const existingBlocks = workspace.getAllBlocks();
            if (existingBlocks.length > 0) {
                existingBlocks.forEach(block => {
                    const blockY = block.getRelativeToSurfaceXY().y;
                    if (blockY > maxY) {
                        maxY = blockY + 60; // Add some spacing
                    }
                });
            }

            // Adjust Y positions of imported blocks
            for (let i = 0; i < blocks.length; i++) {
                const block = blocks[i];
                const currentY = parseInt(block.getAttribute('y') || '0');
                block.setAttribute('y', (maxY + currentY).toString());
            }

            // Add blocks to workspace
            Blockly.Xml.domToWorkspace(xml, workspace);

            // Update the generated code
            updateCode();

            console.log('Blocks imported successfully');
        } catch (error) {
            console.error('Error importing blocks:', error);
            alert('Error importing blocks: ' + error.message);
        }
    }

    function handleImportError(error) {
        console.error('Import error:', error);
        alert('Failed to import file: ' + error);
    }

    // Event handlers
    document.addEventListener('DOMContentLoaded', function() {
        initializeBlockly();

        // Import button
        document.getElementById('importBtn').addEventListener('click', function() {
            vscode.postMessage({
                type: 'import-file'
            });
        });

        // Export button
        document.getElementById('exportBtn').addEventListener('click', function() {
            const code = document.getElementById('codeOutput').textContent;
            vscode.postMessage({
                type: 'export',
                code: code,
                language: currentLanguage
            });
        });

        // Clear button
        document.getElementById('clearBtn').addEventListener('click', function() {
            if (confirm('Are you sure you want to clear the workspace? This action cannot be undone.')) {
                workspace.clear();
            }
        });

        // Language selector
        document.getElementById('languageSelect').addEventListener('change', function(e) {
            currentLanguage = e.target.value;
            updateCode();
        });

        // Notify VS Code that the webview is ready
        vscode.postMessage({ type: 'ready' });
    });

    // Handle messages from VS Code
    window.addEventListener('message', event => {
        const message = event.data;
        switch (message.type) {
            case 'update':
                if (message.content) {
                    loadWorkspace(message.content);
                }
                break;
            case 'file-imported':
                handleFileImported(message.code, message.language, message.fileName);
                break;
            case 'blocks-parsed':
                handleBlocksParsed(message.blocks);
                break;
            case 'import-error':
                handleImportError(message.error);
                break;
        }
    });

    // Handle window resize
    window.addEventListener('resize', function() {
        if (workspace) {
            Blockly.svgResize(workspace);
        }
    });
})();
