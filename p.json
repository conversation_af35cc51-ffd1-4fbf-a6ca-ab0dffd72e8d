{"name": "vsc-blockly", "displayName": "Blockly Visual Programming", "description": "Visual programming with Google Blockly in VS Code", "version": "0.1.0", "publisher": "blockly-vscode", "engines": {"vscode": "^1.74.0"}, "categories": ["Other", "Education"], "keywords": ["blockly", "visual programming", "blocks", "education", "coding"], "activationEvents": ["onCustomEditor:blockly.editor"], "main": "./out/extension.js", "contributes": {"customEditors": [{"viewType": "blockly.editor", "displayName": "<PERSON>ly Editor", "selector": [{"filenamePattern": "*.blockly"}, {"filenamePattern": "*.blocks"}], "priority": "default"}], "commands": [{"command": "blockly.newFile", "title": "New Blockly File", "category": "<PERSON><PERSON>"}, {"command": "blockly.exportCode", "title": "Export Generated Code", "category": "<PERSON><PERSON>"}, {"command": "blockly.clearWorkspace", "title": "Clear Workspace", "category": "<PERSON><PERSON>"}], "menus": {"explorer/context": [{"command": "blockly.newFile", "group": "navigation@1", "when": "explorerResourceIsFolder"}], "commandPalette": [{"command": "blockly.newFile"}, {"command": "blockly.exportCode", "when": "activeCustomEditorId == blockly.editor"}, {"command": "blockly.clearWorkspace", "when": "activeCustomEditorId == blockly.editor"}]}, "languages": [{"id": "blockly", "aliases": ["<PERSON><PERSON>", "blockly"], "extensions": [".blockly", ".blocks"], "configuration": "./language-configuration.json"}]}, "scripts": {"vscode:prepublish": "npm run compile", "compile": "webpack --mode production", "watch": "webpack --mode development --watch", "package": "vsce package", "test": "node ./out/test/runTest.js"}, "devDependencies": {"@types/vscode": "^1.74.0", "@types/node": "16.x", "@typescript-eslint/eslint-plugin": "^5.45.0", "@typescript-eslint/parser": "^5.45.0", "eslint": "^8.28.0", "typescript": "^4.9.4", "webpack": "^5.75.0", "webpack-cli": "^5.0.1", "ts-loader": "^9.4.1", "@vscode/test-electron": "^2.2.0", "@vscode/vsce": "^2.15.0"}, "dependencies": {"blockly": "^10.2.2"}}