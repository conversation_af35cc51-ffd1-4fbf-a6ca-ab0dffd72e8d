import * as vscode from 'vscode';
import { BlocklyDocument, BlocklyDocumentData } from './blocklyDocument';

/**
 * Provider for Blockly custom editors
 */
export class BlocklyEditorProvider implements vscode.CustomTextEditorProvider {
    private static readonly viewType = 'blockly.editor';

    constructor(
        private readonly context: vscode.ExtensionContext
    ) { }

    public static register(context: vscode.ExtensionContext): vscode.Disposable {
        const provider = new BlocklyEditorProvider(context);
        const providerRegistration = vscode.window.registerCustomEditorProvider(
            BlocklyEditorProvider.viewType,
            provider
        );
        return providerRegistration;
    }

    /**
     * Called when our custom editor is opened.
     */
    public async resolveCustomTextEditor(
        document: vscode.TextDocument,
        webviewPanel: vscode.WebviewPanel,
        _token: vscode.CancellationToken
    ): Promise<void> {
        // Setup initial content for the webview
        webviewPanel.webview.options = {
            enableScripts: true,
        };
        webviewPanel.webview.html = this.getHtmlForWebview(webviewPanel.webview);

        function updateWebview() {
            webviewPanel.webview.postMessage({
                type: 'update',
                content: document.getText(),
            });
        }

        // Hook up event handlers so that we can synchronize the webview with the text document.
        const changeDocumentSubscription = vscode.workspace.onDidChangeTextDocument(e => {
            if (e.document.uri.toString() === document.uri.toString()) {
                updateWebview();
            }
        });

        // Make sure we get rid of the listener when our editor is closed.
        webviewPanel.onDidDispose(() => {
            changeDocumentSubscription.dispose();
        });

        // Receive message from the webview.
        webviewPanel.webview.onDidReceiveMessage(e => {
            switch (e.type) {
                case 'save':
                    this.updateTextDocument(document, e.content);
                    return;
                case 'export':
                    this.exportGeneratedCode(e.code, e.language);
                    return;
                case 'ready':
                    updateWebview();
                    return;
                case 'import-file':
                    this.handleFileImport(webviewPanel);
                    return;
                case 'import-code':
                    this.handleCodeImport(e.code, e.language, webviewPanel);
                    return;
            }
        });

        updateWebview();
    }

    /**
     * Get the static html used for the editor webviews.
     */
    private getHtmlForWebview(webview: vscode.Webview): string {
        // Local path to script and css for the webview
        const scriptUri = webview.asWebviewUri(vscode.Uri.joinPath(
            this.context.extensionUri, 'media', 'blocklyEditor.js'));
        const styleUri = webview.asWebviewUri(vscode.Uri.joinPath(
            this.context.extensionUri, 'media', 'blocklyEditor.css'));

        // Use a nonce to whitelist which scripts can be run
        const nonce = getNonce();

        return /* html */`
            <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <meta http-equiv="Content-Security-Policy" content="default-src 'none'; img-src ${webview.cspSource}; style-src ${webview.cspSource} 'unsafe-inline'; script-src 'nonce-${nonce}';">
                <meta name="viewport" content="width=device-width, initial-scale=1.0">
                <link href="${styleUri}" rel="stylesheet" />
                <title>Blockly Editor</title>
            </head>
            <body>
                <div id="toolbar">
                    <button id="importBtn">Import File</button>
                    <button id="exportBtn">Export Code</button>
                    <button id="clearBtn">Clear Workspace</button>
                    <select id="languageSelect">
                        <option value="JavaScript">JavaScript</option>
                        <option value="Python">Python</option>
                        <option value="Dart">Dart</option>
                        <option value="Lua">Lua</option>
                        <option value="PHP">PHP</option>
                    </select>
                </div>
                <div id="container">
                    <div id="blocklyDiv"></div>
                    <div id="codeDiv">
                        <h3>Generated Code:</h3>
                        <pre id="codeOutput"></pre>
                    </div>
                </div>
                <script src="https://unpkg.com/blockly/blockly.min.js" nonce="${nonce}"></script>
                <script nonce="${nonce}" src="${scriptUri}"></script>
            </body>
            </html>`;
    }

    /**
     * Update the text document with new content from the webview
     */
    private updateTextDocument(document: vscode.TextDocument, content: string) {
        const edit = new vscode.WorkspaceEdit();
        
        // Replace the entire document content
        edit.replace(
            document.uri,
            new vscode.Range(0, 0, document.lineCount, 0),
            content
        );

        return vscode.workspace.applyEdit(edit);
    }

    /**
     * Export generated code to a new file
     */
    private async exportGeneratedCode(code: string, language: string) {
        const extension = this.getFileExtension(language);
        const fileName = `generated_code.${extension}`;
        
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            vscode.window.showErrorMessage('Please open a workspace folder first.');
            return;
        }

        const filePath = vscode.Uri.joinPath(workspaceFolder.uri, fileName);
        await vscode.workspace.fs.writeFile(filePath, Buffer.from(code, 'utf8'));
        
        const document = await vscode.workspace.openTextDocument(filePath);
        await vscode.window.showTextDocument(document);
    }

    private getFileExtension(language: string): string {
        switch (language) {
            case 'JavaScript': return 'js';
            case 'Python': return 'py';
            case 'Dart': return 'dart';
            case 'Lua': return 'lua';
            case 'PHP': return 'php';
            default: return 'txt';
        }
    }

    /**
     * Handle file import request from webview
     */
    private async handleFileImport(webviewPanel: vscode.WebviewPanel) {
        const workspaceFolder = vscode.workspace.workspaceFolders?.[0];
        if (!workspaceFolder) {
            vscode.window.showErrorMessage('No workspace folder found');
            return;
        }

        const fileUri = await vscode.window.showOpenDialog({
            canSelectFiles: true,
            canSelectFolders: false,
            canSelectMany: false,
            defaultUri: workspaceFolder.uri,
            filters: {
                'JavaScript/TypeScript': ['js', 'ts', 'jsx', 'tsx'],
                'All Files': ['*']
            },
            openLabel: 'Import File'
        });

        if (fileUri && fileUri[0]) {
            try {
                const fileContent = await vscode.workspace.fs.readFile(fileUri[0]);
                const code = Buffer.from(fileContent).toString('utf8');
                const fileName = fileUri[0].fsPath;
                const language = this.detectLanguageFromFile(fileName);

                // Send the file content to webview for parsing
                webviewPanel.webview.postMessage({
                    type: 'file-imported',
                    code: code,
                    language: language,
                    fileName: fileName
                });

            } catch (error) {
                vscode.window.showErrorMessage(`Failed to read file: ${error}`);
            }
        }
    }

    /**
     * Handle code import and conversion to blocks
     */
    private async handleCodeImport(code: string, language: string, webviewPanel: vscode.WebviewPanel) {
        try {
            const { CodeParser } = await import('./codeParser');
            const parser = new CodeParser();
            const blocks = await parser.parseCodeToBlocks(code, language);

            webviewPanel.webview.postMessage({
                type: 'blocks-parsed',
                blocks: blocks
            });

        } catch (error) {
            vscode.window.showErrorMessage(`Failed to parse code: ${error}`);
            webviewPanel.webview.postMessage({
                type: 'import-error',
                error: `Failed to parse code: ${error}`
            });
        }
    }

    /**
     * Detect programming language from file extension
     */
    private detectLanguageFromFile(fileName: string): string {
        const extension = fileName.toLowerCase().split('.').pop();
        switch (extension) {
            case 'js':
            case 'jsx':
                return 'JavaScript';
            case 'ts':
            case 'tsx':
                return 'TypeScript';
            default:
                return 'JavaScript'; // Default fallback
        }
    }
}

function getNonce() {
    let text = '';
    const possible = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    for (let i = 0; i < 32; i++) {
        text += possible.charAt(Math.floor(Math.random() * possible.length));
    }
    return text;
}
