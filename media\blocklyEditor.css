/* Blockly Editor Styles */
body {
    margin: 0;
    padding: 0;
    font-family: var(--vscode-font-family);
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    height: 100vh;
    overflow: hidden;
}

#toolbar {
    display: flex;
    align-items: center;
    gap: 10px;
    padding: 8px 12px;
    background-color: var(--vscode-titleBar-activeBackground);
    border-bottom: 1px solid var(--vscode-panel-border);
    flex-shrink: 0;
}

#toolbar button {
    background-color: var(--vscode-button-background);
    color: var(--vscode-button-foreground);
    border: none;
    padding: 6px 12px;
    border-radius: 3px;
    cursor: pointer;
    font-size: 12px;
    font-family: var(--vscode-font-family);
}

#toolbar button:hover {
    background-color: var(--vscode-button-hoverBackground);
}

#importBtn {
    background-color: var(--vscode-button-secondaryBackground, #0e639c);
    color: var(--vscode-button-secondaryForeground, #ffffff);
}

#importBtn:hover {
    background-color: var(--vscode-button-secondaryHoverBackground, #1177bb);
}

#toolbar select {
    background-color: var(--vscode-dropdown-background);
    color: var(--vscode-dropdown-foreground);
    border: 1px solid var(--vscode-dropdown-border);
    padding: 4px 8px;
    border-radius: 3px;
    font-size: 12px;
    font-family: var(--vscode-font-family);
}

#container {
    display: flex;
    height: calc(100vh - 45px);
    overflow: hidden;
}

#blocklyDiv {
    flex: 1;
    height: 100%;
    min-width: 400px;
}

#codeDiv {
    width: 350px;
    background-color: var(--vscode-sideBar-background);
    border-left: 1px solid var(--vscode-panel-border);
    display: flex;
    flex-direction: column;
    overflow: hidden;
}

#codeDiv h3 {
    margin: 0;
    padding: 12px;
    background-color: var(--vscode-sideBarSectionHeader-background);
    color: var(--vscode-sideBarSectionHeader-foreground);
    font-size: 13px;
    font-weight: 600;
    border-bottom: 1px solid var(--vscode-panel-border);
}

#codeOutput {
    flex: 1;
    margin: 0;
    padding: 12px;
    background-color: var(--vscode-editor-background);
    color: var(--vscode-editor-foreground);
    font-family: var(--vscode-editor-font-family);
    font-size: var(--vscode-editor-font-size);
    overflow: auto;
    white-space: pre-wrap;
    word-wrap: break-word;
    border: none;
}

/* Blockly workspace styling */
.blocklyToolboxDiv {
    background-color: var(--vscode-sideBar-background) !important;
    border-right: 1px solid var(--vscode-panel-border) !important;
}

.blocklyTreeLabel {
    color: var(--vscode-sideBar-foreground) !important;
}

.blocklyTreeSelected .blocklyTreeLabel {
    color: var(--vscode-list-activeSelectionForeground) !important;
}

.blocklyTreeSelected {
    background-color: var(--vscode-list-activeSelectionBackground) !important;
}

.blocklyFlyoutBackground {
    fill: var(--vscode-sideBar-background) !important;
}

.blocklyMainBackground {
    fill: var(--vscode-editor-background) !important;
}

/* Responsive design */
@media (max-width: 800px) {
    #container {
        flex-direction: column;
    }
    
    #codeDiv {
        width: 100%;
        height: 200px;
        border-left: none;
        border-top: 1px solid var(--vscode-panel-border);
    }
    
    #blocklyDiv {
        min-width: unset;
        height: calc(100% - 200px);
    }
}

/* Scrollbar styling for VS Code theme consistency */
::-webkit-scrollbar {
    width: 10px;
    height: 10px;
}

::-webkit-scrollbar-track {
    background: var(--vscode-scrollbarSlider-background);
}

::-webkit-scrollbar-thumb {
    background: var(--vscode-scrollbarSlider-background);
    border-radius: 5px;
}

::-webkit-scrollbar-thumb:hover {
    background: var(--vscode-scrollbarSlider-hoverBackground);
}

::-webkit-scrollbar-corner {
    background: var(--vscode-editor-background);
}
