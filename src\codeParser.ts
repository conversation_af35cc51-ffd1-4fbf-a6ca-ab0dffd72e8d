import * as vscode from 'vscode';

/**
 * Interface for parsed block data
 */
export interface ParsedBlock {
    type: string;
    id: string;
    x: number;
    y: number;
    fields?: { [key: string]: string };
    values?: { [key: string]: ParsedBlock };
    statements?: { [key: string]: ParsedBlock[] };
    next?: ParsedBlock;
}

/**
 * Code parser for converting JavaScript/TypeScript to Blockly blocks
 */
export class CodeParser {
    private blockIdCounter = 0;
    private currentY = 20;
    private readonly BLOCK_HEIGHT = 40;

    /**
     * Parse code string into Blockly blocks
     */
    public async parseCodeToBlocks(code: string, language: string): Promise<string> {
        this.blockIdCounter = 0;
        this.currentY = 20;

        try {
            const blocks = this.parseStatements(code);
            return this.blocksToXml(blocks);
        } catch (error) {
            throw new Error(`Failed to parse ${language} code: ${error}`);
        }
    }

    /**
     * Parse code statements into block objects
     */
    private parseStatements(code: string): ParsedBlock[] {
        const blocks: ParsedBlock[] = [];
        const lines = code.split('\n').map(line => line.trim()).filter(line => line.length > 0);

        for (const line of lines) {
            const block = this.parseStatement(line);
            if (block) {
                blocks.push(block);
                this.currentY += this.BLOCK_HEIGHT;
            }
        }

        return blocks;
    }

    /**
     * Parse a single statement into a block
     */
    private parseStatement(statement: string): ParsedBlock | null {
        // Remove semicolons and trim
        statement = statement.replace(/;$/, '').trim();

        // Skip empty statements and comments
        if (!statement || statement.startsWith('//') || statement.startsWith('/*')) {
            return null;
        }

        // Variable declarations
        if (statement.match(/^(let|const|var)\s+\w+\s*=/)) {
            return this.parseVariableDeclaration(statement);
        }

        // Function calls (including console.log, alert, etc.)
        if (statement.match(/\w+\s*\([^)]*\)$/)) {
            return this.parseFunctionCall(statement);
        }

        // Assignment statements
        if (statement.match(/^\w+\s*=\s*.+/)) {
            return this.parseAssignment(statement);
        }

        // If statements
        if (statement.startsWith('if')) {
            return this.parseIfStatement(statement);
        }

        // For loops
        if (statement.startsWith('for')) {
            return this.parseForLoop(statement);
        }

        // While loops
        if (statement.startsWith('while')) {
            return this.parseWhileLoop(statement);
        }

        // Return statements
        if (statement.startsWith('return')) {
            return this.parseReturnStatement(statement);
        }

        // Default: create a text block for unrecognized statements
        return this.createTextBlock(statement);
    }

    /**
     * Parse variable declaration
     */
    private parseVariableDeclaration(statement: string): ParsedBlock {
        const match = statement.match(/^(let|const|var)\s+(\w+)\s*=\s*(.+)/);
        if (match) {
            const [, , varName, value] = match;
            return {
                type: 'variables_set',
                id: this.generateId(),
                x: 20,
                y: this.currentY,
                fields: {
                    'VAR': varName
                },
                values: {
                    'VALUE': this.parseValue(value)
                }
            };
        }
        return this.createTextBlock(statement);
    }

    /**
     * Parse function call
     */
    private parseFunctionCall(statement: string): ParsedBlock {
        // Handle console.log specifically
        if (statement.startsWith('console.log(')) {
            const content = statement.slice(12, -1); // Remove 'console.log(' and ')'
            return {
                type: 'text_print',
                id: this.generateId(),
                x: 20,
                y: this.currentY,
                values: {
                    'TEXT': this.parseValue(content)
                }
            };
        }

        // Handle alert specifically
        if (statement.startsWith('alert(')) {
            const content = statement.slice(6, -1); // Remove 'alert(' and ')'
            return {
                type: 'text_print',
                id: this.generateId(),
                x: 20,
                y: this.currentY,
                values: {
                    'TEXT': this.parseValue(content)
                }
            };
        }

        // Generic function call
        const match = statement.match(/^(\w+)\s*\(([^)]*)\)$/);
        if (match) {
            const [, functionName, args] = match;
            return {
                type: 'procedures_callnoreturn',
                id: this.generateId(),
                x: 20,
                y: this.currentY,
                fields: {
                    'NAME': functionName
                }
            };
        }

        return this.createTextBlock(statement);
    }

    /**
     * Parse assignment statement
     */
    private parseAssignment(statement: string): ParsedBlock {
        const match = statement.match(/^(\w+)\s*=\s*(.+)/);
        if (match) {
            const [, varName, value] = match;
            return {
                type: 'variables_set',
                id: this.generateId(),
                x: 20,
                y: this.currentY,
                fields: {
                    'VAR': varName
                },
                values: {
                    'VALUE': this.parseValue(value)
                }
            };
        }
        return this.createTextBlock(statement);
    }

    /**
     * Parse if statement (simplified)
     */
    private parseIfStatement(statement: string): ParsedBlock {
        const match = statement.match(/^if\s*\(([^)]+)\)/);
        if (match) {
            const condition = match[1];
            return {
                type: 'controls_if',
                id: this.generateId(),
                x: 20,
                y: this.currentY,
                values: {
                    'IF0': this.parseCondition(condition)
                }
            };
        }
        return this.createTextBlock(statement);
    }

    /**
     * Parse for loop (simplified)
     */
    private parseForLoop(statement: string): ParsedBlock {
        return {
            type: 'controls_repeat_ext',
            id: this.generateId(),
            x: 20,
            y: this.currentY,
            values: {
                'TIMES': {
                    type: 'math_number',
                    id: this.generateId(),
                    x: 0,
                    y: 0,
                    fields: {
                        'NUM': '10'
                    }
                }
            }
        };
    }

    /**
     * Parse while loop (simplified)
     */
    private parseWhileLoop(statement: string): ParsedBlock {
        const match = statement.match(/^while\s*\(([^)]+)\)/);
        if (match) {
            const condition = match[1];
            return {
                type: 'controls_whileUntil',
                id: this.generateId(),
                x: 20,
                y: this.currentY,
                fields: {
                    'MODE': 'WHILE'
                },
                values: {
                    'BOOL': this.parseCondition(condition)
                }
            };
        }
        return this.createTextBlock(statement);
    }

    /**
     * Parse return statement
     */
    private parseReturnStatement(statement: string): ParsedBlock {
        const match = statement.match(/^return\s*(.+)?/);
        const returnValue = match && match[1] ? match[1] : '';
        
        return {
            type: 'procedures_return',
            id: this.generateId(),
            x: 20,
            y: this.currentY,
            values: returnValue ? {
                'VALUE': this.parseValue(returnValue)
            } : undefined
        };
    }

    /**
     * Parse a value expression
     */
    private parseValue(value: string): ParsedBlock {
        value = value.trim();

        // String literals
        if ((value.startsWith('"') && value.endsWith('"')) || 
            (value.startsWith("'") && value.endsWith("'"))) {
            return {
                type: 'text',
                id: this.generateId(),
                x: 0,
                y: 0,
                fields: {
                    'TEXT': value.slice(1, -1) // Remove quotes
                }
            };
        }

        // Number literals
        if (/^\d+(\.\d+)?$/.test(value)) {
            return {
                type: 'math_number',
                id: this.generateId(),
                x: 0,
                y: 0,
                fields: {
                    'NUM': value
                }
            };
        }

        // Boolean literals
        if (value === 'true' || value === 'false') {
            return {
                type: 'logic_boolean',
                id: this.generateId(),
                x: 0,
                y: 0,
                fields: {
                    'BOOL': value.toUpperCase()
                }
            };
        }

        // Variables
        if (/^\w+$/.test(value)) {
            return {
                type: 'variables_get',
                id: this.generateId(),
                x: 0,
                y: 0,
                fields: {
                    'VAR': value
                }
            };
        }

        // Default: text block
        return {
            type: 'text',
            id: this.generateId(),
            x: 0,
            y: 0,
            fields: {
                'TEXT': value
            }
        };
    }

    /**
     * Parse a condition expression
     */
    private parseCondition(condition: string): ParsedBlock {
        condition = condition.trim();

        // Simple comparisons
        const comparisonMatch = condition.match(/^(.+?)\s*(==|!=|<|>|<=|>=)\s*(.+)$/);
        if (comparisonMatch) {
            const [, left, operator, right] = comparisonMatch;
            const blockOperator = this.mapComparisonOperator(operator);
            
            return {
                type: 'logic_compare',
                id: this.generateId(),
                x: 0,
                y: 0,
                fields: {
                    'OP': blockOperator
                },
                values: {
                    'A': this.parseValue(left),
                    'B': this.parseValue(right)
                }
            };
        }

        // Default: boolean value
        return this.parseValue(condition);
    }

    /**
     * Map JavaScript comparison operators to Blockly operators
     */
    private mapComparisonOperator(operator: string): string {
        switch (operator) {
            case '==': return 'EQ';
            case '!=': return 'NEQ';
            case '<': return 'LT';
            case '>': return 'GT';
            case '<=': return 'LTE';
            case '>=': return 'GTE';
            default: return 'EQ';
        }
    }

    /**
     * Create a text block for unrecognized statements
     */
    private createTextBlock(text: string): ParsedBlock {
        return {
            type: 'text',
            id: this.generateId(),
            x: 20,
            y: this.currentY,
            fields: {
                'TEXT': text
            }
        };
    }

    /**
     * Convert blocks to Blockly XML format
     */
    private blocksToXml(blocks: ParsedBlock[]): string {
        const xmlBlocks = blocks.map(block => this.blockToXml(block)).join('');
        return `<xml xmlns="https://developers.google.com/blockly/xml">${xmlBlocks}</xml>`;
    }

    /**
     * Convert a single block to XML
     */
    private blockToXml(block: ParsedBlock): string {
        let xml = `<block type="${block.type}" id="${block.id}" x="${block.x}" y="${block.y}">`;

        // Add fields
        if (block.fields) {
            for (const [name, value] of Object.entries(block.fields)) {
                xml += `<field name="${name}">${this.escapeXml(value)}</field>`;
            }
        }

        // Add values
        if (block.values) {
            for (const [name, valueBlock] of Object.entries(block.values)) {
                xml += `<value name="${name}">${this.blockToXml(valueBlock)}</value>`;
            }
        }

        // Add statements
        if (block.statements) {
            for (const [name, statementBlocks] of Object.entries(block.statements)) {
                xml += `<statement name="${name}">`;
                for (const statementBlock of statementBlocks) {
                    xml += this.blockToXml(statementBlock);
                }
                xml += `</statement>`;
            }
        }

        // Add next block
        if (block.next) {
            xml += `<next>${this.blockToXml(block.next)}</next>`;
        }

        xml += `</block>`;
        return xml;
    }

    /**
     * Escape XML special characters
     */
    private escapeXml(text: string): string {
        return text
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#39;');
    }

    /**
     * Generate unique block ID
     */
    private generateId(): string {
        return `block_${++this.blockIdCounter}`;
    }
}
